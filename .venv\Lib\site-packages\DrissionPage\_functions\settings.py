# -*- coding:utf-8 -*-
"""
<AUTHOR> g1879
@Contact  : <EMAIL>
@Copyright: (c) 2024 by g1879, Inc. All Rights Reserved.
@License  : BSD 3-Clause.
"""
from pathlib import Path


class Settings(object):
    raise_when_ele_not_found = False
    raise_when_click_failed = False
    raise_when_wait_failed = False
    singleton_tab_obj = True
    cdp_timeout = 30
    auto_handle_alert = None
    locate_suffixes_list = str(Path(__file__).parent.absolute() / 'suffixes.dat').replace('\\', '/')

    def set_suffixes_list_path(self, path):
        Settings.locate_suffixes_list = str(Path(path).absolute())
